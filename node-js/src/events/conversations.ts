import type { ConversationType } from "@prisma/client";
import type { Server as SocketServer } from "socket.io";

import type { AuthenticatedSocket } from "~/socket";

import { prisma } from "~/lib/prisma";

async function handleRetrieveConversations({
  io,
  socket,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Conversations retrieved for: ", { user: userAuth });

    const conversations = await prisma.conversation.findMany({
      where: {
        members: {
          some: {
            id: userAuth.id,
          },
        },
      },
    });

    socket.join(userAuth.id);

    console.log("Conversations retrieved", conversations);
  } catch (error) {
    console.error("Error retrieving conversations:", error);
  }
}

async function handleDetailsConversations({
  io,
  socket,
  type,
  referenceId,
  memberAuthId,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  type: ConversationType;
  referenceId?: string;
  memberAuthId: string;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Conversation details for: ", {
      type,
      referenceId,
      userAuthId: userAuth.id,
      memberAuthId,
    });

    let conversation = await prisma.conversation.findFirst({
      where: {
        type,
        referenceId,
        AND: [
          { members: { some: { authId: userAuth.id } } },
          { members: { some: { authId: memberAuthId } } },
        ],
      },
    });

    console.log("Found conversation", conversation);

    if (!conversation) {
      conversation = await prisma.conversation.create({
        data: {
          type,
          referenceId,
        },
      });

      await prisma.conversationToAuth.createMany({
        data: [
          {
            conversationId: conversation.id,
            authId: userAuth.id,
          },
          {
            conversationId: conversation.id,
            authId: memberAuthId,
          },
        ],
      });

      if (referenceId) {
        const forType =
          type === "VENDOR"
            ? "for order"
            : type === "LOGISTIC"
              ? "for delivery"
              : "";

        await prisma.message.create({
          data: {
            content: `Conversation created ${forType} with ID ${referenceId}`,
            conversation: {
              connect: { id: conversation.id },
            },
            sender: {
              connect: { id: userAuth.id },
            },
          },
        });
      }
    }

    socket.join(conversation.id);

    console.log("Conversation details", conversation);
  } catch (error) {
    console.error("Error retrieving conversation details:", error);
  }
}

async function handleSendMessages({
  io,
  socket,
  conversationId,
  content,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  conversationId: string;
  content: string;
}) {
  const userAuth = socket.request.user;

  try {
    console.log("Message sent for: ", { conversationId, content });

    const message = await prisma.message.create({
      data: {
        content,
        conversation: {
          connect: { id: conversationId },
        },
        sender: {
          connect: { id: userAuth.id },
        },
      },
    });

    console.log("Message sent", message);
  } catch (error) {
    console.error("Error sending message:", error);
  }
}

async function handleReadMessages({
  io,
  socket,
  messageId,
}: {
  io: SocketServer;
  socket: AuthenticatedSocket;
  messageId: string;
}) {
  try {
    console.log("Message read for: ", { messageId });

    const message = await prisma.message.update({
      where: { id: messageId },
      data: { isRead: true },
    });

    console.log("Message read", message);
  } catch (error) {
    console.error("Error marking message as read:", error);
  }
}

export {
  handleRetrieveConversations,
  handleDetailsConversations,
  handleSendMessages,
  handleReadMessages,
};
