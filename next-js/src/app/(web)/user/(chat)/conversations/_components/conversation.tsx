"use client";

import { useEffect, useRef } from "react";

import type { PublicConversation, PublicMessage } from "~/lib/types";
import { ConversationEndedBy } from "~/lib/types";
import { cn } from "~/lib/utils";
import { ConversationHeader } from "./conversation-header";
import { ConversationInput } from "./conversation-input";
import { MessageBubble } from "./message-bubble";

interface ConversationProps {
  conversation: PublicConversation;
  messages: PublicMessage[];
  currentUserId: string;
}

export function Conversation({
  conversation,
  messages,
  currentUserId,
}: ConversationProps) {
  const isEnded = !!conversation.endedBy;

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: We want to scroll to bottom whenever messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getDisabledReason = () => {
    if (conversation.endedBy === ConversationEndedBy.SYSTEM) {
      return "This conversation has been ended by the system";
    }
    if (conversation.endedBy === ConversationEndedBy.ADMIN) {
      return "This conversation has been ended by an administrator";
    }
    return "This conversation has been ended";
  };

  return (
    <div className={cn("flex flex-col h-full")}>
      <ConversationHeader conversation={conversation} />
      <div
        className={cn(
          "flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900",
          isEnded && "bg-red-50/50 dark:bg-red-950/10",
        )}
      >
        {messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
            isOwnMessage={message.sender.id === currentUserId}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>
      <ConversationInput
        onSendMessage={() => {}}
        isDisabled={isEnded}
        disabledReason={getDisabledReason()}
      />
    </div>
  );
}
