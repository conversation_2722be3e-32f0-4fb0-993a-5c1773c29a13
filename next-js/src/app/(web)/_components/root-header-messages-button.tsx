"use client";

import Link from "next/link";

import { BellRingIcon } from "lucide-react";

import { buttonVariants } from "~/components/ui/button";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";

export function RootHeaderMessagesButton() {
  const { auth } = useAuthContext();

  if (auth && auth.role !== "USER") {
    return null;
  }

  return (
    <Link
      className={buttonVariants({
        variant: "outline",
        size: "icon",
        className: cn("relative"),
      })}
      href={routes.app.user.conversations.url()}
    >
      <BellRingIcon className="size-5" />
    </Link>
  );
}
