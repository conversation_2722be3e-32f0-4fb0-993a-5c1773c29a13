import { type Socket, io } from "socket.io-client";
import { events } from "./events";

class SocketClient {
  private static instance: SocketClient;
  private socket: Socket | null = null;

  private constructor() {}

  static getInstance(): SocketClient {
    if (!SocketClient.instance) {
      SocketClient.instance = new SocketClient();
    }
    return SocketClient.instance;
  }

  connect(token: string) {
    if (this.socket?.connected) return;

    this.socket = io(
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080",
      {
        extraHeaders: {
          Authorization: `Bearer ${token}`,
        },
      },
    );

    this.socket.on(events.app.connection, () => {
      console.log("Connected to socket server");
    });

    this.socket.on(events.app.disconnect, () => {
      console.log("Disconnected from socket server");
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSocket() {
    return this.socket;
  }
}

export const socketClient = SocketClient.getInstance();
